import { Controller, Get, Req, Res } from '@nestjs/common';
import { Response, Request } from 'express';
import { ExotelService } from './exotel.service';

@Controller('thirdParty/exotel')
export class ExotelController {
  constructor(private readonly service: ExotelService) {}

  @Get('scheduleCallbackHandler')
  async funScheduleCallbackHandler(@Req() req: Request, @Res() res: Response) {
    const data = await this.service.scheduleCallbackHandler(req?.query);
    res.status(200).send(data);
  }

  @Get('connectUserToAgent')
  async funConnectUserToAgent(@Req() req: Request, @Res() res: Response) {
    const data = await this.service.connectUserToAgent(req?.query);
    res.status(200).send(data);
  }

  @Get('connectUserToSupport')
  async funConnectUserToSupport(@Req() req: Request, @Res() res: Response) {
    const data = await this.service.connectToSupport(req?.query?.flow_id);
    res.status(200).send(data);
  }
}
